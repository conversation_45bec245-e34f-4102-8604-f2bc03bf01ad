/**
 * Discount Engine Implementation
 * 
 * This file provides the concrete implementation of the discount calculation engine
 * with pluggable calculator support and extensible architecture.
 */

import type { 
  Discount, 
  DiscountCalculationResult, 
  CartItemWithDetails,
  DiscountType,
  AppliedDiscount
} from './discount.types';
import type {
  DiscountEngine,
  DiscountCalculator,
  DiscountEngineConfig,
  DiscountEngineValidationResult,
  DiscountEngineFactory
} from './discount.engine.interface';
import {
  CalculatorRegistrationError,
  DiscountEngineError,
  DiscountEngineUtils
} from './discount.engine.interface';
import { logger } from '@/lib/logger';
import { getEngineConfig } from './discount.config';

/**
 * Concrete implementation of the discount engine
 */
export class DefaultDiscountEngine implements DiscountEngine {
  private calculators = new Map<DiscountType, DiscountCalculator>();
  private config: DiscountEngineConfig;
  private stats = {
    totalCalculations: 0,
    totalCalculationTimeMs: 0,
    lastCalculationAt: undefined as Date | undefined,
    errors: [] as Array<{
      timestamp: Date;
      error: string;
      discountType?: DiscountType;
    }>
  };

  constructor(config: DiscountEngineConfig = {}) {
    // Load configuration from centralized config system
    const engineConfig = getEngineConfig();
    const DEFAULT_CONFIG: DiscountEngineConfig = {
      maxDiscountsPerCart: engineConfig.maxDiscountsPerCart,
      allowStacking: engineConfig.allowStacking,
      calculationTimeoutMs: engineConfig.calculationTimeoutMs,
      enableDebugLogging: engineConfig.enableDebugLogging
    };
    
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  async calculateDiscounts(
    cartItems: CartItemWithDetails[],
    availableDiscounts: Discount[]
  ): Promise<DiscountCalculationResult> {
    const startTime = Date.now();
    
    try {
      // Validate inputs
      const cartValidationErrors = DiscountEngineUtils.validateCartItems(cartItems);
      if (cartValidationErrors.length > 0) {
        throw new DiscountEngineError(`Invalid cart items: ${cartValidationErrors.join(', ')}`, 'VALIDATION_ERROR');
      }

      if (this.config.enableDebugLogging) {
        logger.debug(`[DiscountEngine] Calculating discounts for ${cartItems.length} items with ${availableDiscounts.length} available discounts`);
      }

      // Calculate original cart total
      const originalTotal = DiscountEngineUtils.calculateCartTotal(cartItems);

      // Filter and sort applicable discounts
      const applicableDiscounts = this.filterApplicableDiscounts(availableDiscounts, cartItems);
      const sortedDiscounts = DiscountEngineUtils.sortDiscountsByPriority(applicableDiscounts);

      // Apply discounts based on stacking configuration
      const appliedDiscounts = this.config.allowStacking 
        ? await this.applyStackedDiscounts(sortedDiscounts, cartItems)
        : await this.applyBestDiscount(sortedDiscounts, cartItems);

      // Calculate totals
      const totalDiscount = appliedDiscounts.reduce((sum, discount) => sum + discount.discountAmount, 0);
      const finalTotal = Math.max(0, originalTotal - totalDiscount); // Ensure non-negative total
      const savings = totalDiscount;

      const result: DiscountCalculationResult = {
        totalDiscount,
        appliedDiscounts,
        originalTotal,
        finalTotal,
        savings
      };

      // Update statistics
      this.updateStats(startTime);

      if (this.config.enableDebugLogging) {
        logger.debug(`[DiscountEngine] Calculation complete:`, result);
      }

      return result;

    } catch (error) {
      this.recordError(error instanceof Error ? error.message : 'Unknown error');
      throw error;
    }
  }

  registerCalculator(calculator: DiscountCalculator): void {
    if (this.calculators.has(calculator.type)) {
      throw new CalculatorRegistrationError(
        calculator.type, 
        `Calculator for type ${calculator.type} is already registered`
      );
    }

    // Validate calculator implementation
    if (typeof calculator.canApply !== 'function') {
      throw new CalculatorRegistrationError(calculator.type, 'Calculator must implement canApply method');
    }

    if (typeof calculator.calculate !== 'function') {
      throw new CalculatorRegistrationError(calculator.type, 'Calculator must implement calculate method');
    }

    if (typeof calculator.validate !== 'function') {
      throw new CalculatorRegistrationError(calculator.type, 'Calculator must implement validate method');
    }

    this.calculators.set(calculator.type, calculator);

    if (this.config.enableDebugLogging) {
      logger.debug(`[DiscountEngine] Registered calculator for type: ${calculator.type}`);
    }
  }

  unregisterCalculator(type: DiscountType): boolean {
    const existed = this.calculators.has(type);
    this.calculators.delete(type);

    if (existed && this.config.enableDebugLogging) {
      logger.debug(`[DiscountEngine] Unregistered calculator for type: ${type}`);
    }

    return existed;
  }

  getCalculator(type: DiscountType): DiscountCalculator | undefined {
    return this.calculators.get(type);
  }

  getRegisteredTypes(): DiscountType[] {
    return Array.from(this.calculators.keys());
  }

  validateDiscount(discount: Discount): DiscountEngineValidationResult {
    const calculator = this.calculators.get(discount.type);
    
    if (!calculator) {
      return {
        isValid: false,
        errors: [`No calculator registered for discount type: ${discount.type}`],
        warnings: []
      };
    }

    try {
      const errors = calculator.validate(discount);
      return {
        isValid: errors.length === 0,
        errors,
        warnings: []
      };
    } catch (error) {
      return {
        isValid: false,
        errors: [`Validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`],
        warnings: []
      };
    }
  }

  validateDiscountCombination(discounts: Discount[]): DiscountEngineValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check individual discount validity
    for (const discount of discounts) {
      const validation = this.validateDiscount(discount);
      errors.push(...validation.errors);
      warnings.push(...validation.warnings);
    }

    // Check combination rules
    if (!this.config.allowStacking && discounts.length > 1) {
      errors.push('Discount stacking is not allowed');
    }

    if (discounts.length > (this.config.maxDiscountsPerCart || 10)) {
      errors.push(`Too many discounts: maximum ${this.config.maxDiscountsPerCart} allowed`);
    }

    // Check for conflicting discount types
    const typeGroups = new Map<DiscountType, Discount[]>();
    for (const discount of discounts) {
      if (!typeGroups.has(discount.type)) {
        typeGroups.set(discount.type, []);
      }
      typeGroups.get(discount.type)!.push(discount);
    }

    for (const [type, groupDiscounts] of typeGroups) {
      if (groupDiscounts.length > 1) {
        warnings.push(`Multiple discounts of type ${type} may conflict`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  getConfig(): DiscountEngineConfig {
    return { ...this.config };
  }

  updateConfig(config: Partial<DiscountEngineConfig>): void {
    this.config = { ...this.config, ...config };

    if (this.config.enableDebugLogging) {
      logger.debug(`[DiscountEngine] Configuration updated:`, this.config);
    }
  }

  getEngineStats() {
    const averageCalculationTimeMs = this.stats.totalCalculations > 0 
      ? this.stats.totalCalculationTimeMs / this.stats.totalCalculations 
      : 0;

    return {
      registeredCalculators: this.calculators.size,
      totalCalculations: this.stats.totalCalculations,
      averageCalculationTimeMs: Math.round(averageCalculationTimeMs * 100) / 100,
      lastCalculationAt: this.stats.lastCalculationAt,
      errors: {
        total: this.stats.errors.length,
        recent: this.stats.errors.slice(-10) // Last 10 errors
      }
    };
  }

  /**
   * Filter discounts that can be applied to the given cart
   */
  private filterApplicableDiscounts(
    discounts: Discount[], 
    cartItems: CartItemWithDetails[]
  ): Discount[] {
    const applicable: Discount[] = [];

    for (const discount of discounts) {
      const calculator = this.calculators.get(discount.type);
      
      if (!calculator) {
        if (this.config.enableDebugLogging) {
          logger.warn(`[DiscountEngine] No calculator found for discount type: ${discount.type}`);
        }
        continue;
      }

      try {
        if (calculator.canApply(discount, cartItems)) {
          applicable.push(discount);
        }
      } catch (error) {
        this.recordError(`Error checking applicability for discount ${discount.id}: ${error instanceof Error ? error.message : 'Unknown error'}`, discount.type);
      }
    }

    return applicable;
  }

  /**
   * Apply the best single discount (non-stacking mode)
   */
  private async applyBestDiscount(
    discounts: Discount[], 
    cartItems: CartItemWithDetails[]
  ): Promise<AppliedDiscount[]> {
    let bestDiscount: AppliedDiscount | null = null;
    let bestAmount = 0;

    for (const discount of discounts) {
      const calculator = this.calculators.get(discount.type);
      if (!calculator) continue;

      try {
        const amount = calculator.calculate(discount, cartItems);
        
        if (amount > bestAmount) {
          bestAmount = amount;
          bestDiscount = {
            discountId: discount.id,
            discountName: discount.name,
            discountAmount: amount,
            discountType: discount.type
          };
        }
      } catch (error) {
        this.recordError(`Error calculating discount ${discount.id}: ${error instanceof Error ? error.message : 'Unknown error'}`, discount.type);
      }
    }

    return bestDiscount ? [bestDiscount] : [];
  }

  /**
   * Apply multiple discounts (stacking mode)
   */
  private async applyStackedDiscounts(
    discounts: Discount[], 
    cartItems: CartItemWithDetails[]
  ): Promise<AppliedDiscount[]> {
    const applied: AppliedDiscount[] = [];
    const maxDiscounts = this.config.maxDiscountsPerCart || 10;

    for (const discount of discounts.slice(0, maxDiscounts)) {
      const calculator = this.calculators.get(discount.type);
      if (!calculator) continue;

      try {
        const amount = calculator.calculate(discount, cartItems);
        
        if (amount > 0) {
          applied.push({
            discountId: discount.id,
            discountName: discount.name,
            discountAmount: amount,
            discountType: discount.type
          });
        }
      } catch (error) {
        this.recordError(`Error calculating stacked discount ${discount.id}: ${error instanceof Error ? error.message : 'Unknown error'}`, discount.type);
      }
    }

    return applied;
  }

  /**
   * Update engine statistics
   */
  private updateStats(startTime: number): void {
    const calculationTime = Date.now() - startTime;
    this.stats.totalCalculations++;
    this.stats.totalCalculationTimeMs += calculationTime;
    this.stats.lastCalculationAt = new Date();
  }

  /**
   * Record an error for statistics
   */
  private recordError(error: string, discountType?: DiscountType): void {
    this.stats.errors.push({
      timestamp: new Date(),
      error,
      discountType
    });

    // Keep only last 100 errors to prevent memory leaks
    if (this.stats.errors.length > 100) {
      this.stats.errors = this.stats.errors.slice(-100);
    }

    if (this.config.enableDebugLogging) {
      logger.error(`[DiscountEngine] Error recorded:`, { error, discountType });
    }
  }
}

/**
 * Factory implementation for creating discount engines
 */
export class DefaultDiscountEngineFactory implements DiscountEngineFactory {
  createEngine(config?: DiscountEngineConfig): DiscountEngine {
    return new DefaultDiscountEngine(config);
  }

  async createEngineWithDefaults(config?: DiscountEngineConfig): Promise<DiscountEngine> {
    const engine = new DefaultDiscountEngine(config);
    
    // Register default calculators
    const { PercentageCapDiscountCalculator } = await import('./calculators/percentage-cap.calculator');
    engine.registerCalculator(new PercentageCapDiscountCalculator());
    
    return engine;
  }
}

/**
 * Singleton instance for global use
 */
let defaultEngineInstance: DiscountEngine | null = null;

/**
 * Get the default discount engine instance
 */
export function getDefaultDiscountEngine(config?: DiscountEngineConfig): DiscountEngine {
  if (!defaultEngineInstance) {
    defaultEngineInstance = new DefaultDiscountEngine(config);
  }
  return defaultEngineInstance;
}

/**
 * Reset the default engine instance (useful for testing)
 */
export function resetDefaultDiscountEngine(): void {
  defaultEngineInstance = null;
}

/**
 * Create a new discount engine factory
 */
export function createDiscountEngineFactory(): DiscountEngineFactory {
  return new DefaultDiscountEngineFactory();
}