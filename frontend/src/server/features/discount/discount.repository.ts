/**
 * Discount Repository
 * 
 * This repository provides a high-level interface for discount data operations,
 * abstracting the underlying storage provider and adding business logic.
 */

import type { 
  Discount, 
  DiscountFilters, 
  DiscountUsageEntry,
  DiscountType,
  CreateDiscountData
} from './discount.types';
import type {
  DiscountStorageProvider,
  EnhancedDiscountFilters,
  PaginationOptions,
  PaginatedResult,
  UsageStats
} from './storage/storage.interface';
import { InMemoryDiscountStorageProvider } from './storage/in-memory.provider';
import { DiscountNotFoundError } from '../../shared/utils/errors';
import { logger } from '@/lib/logger';

/**
 * Repository interface for discount operations
 */
export interface DiscountRepository {
  // CRUD operations
  create(discountData: CreateDiscountData): Promise<Discount>;
  findById(id: string): Promise<Discount | null>;
  findAll(filters?: EnhancedDiscountFilters, pagination?: PaginationOptions): Promise<PaginatedResult<Discount>>;
  update(id: string, updates: Partial<Discount>): Promise<Discount>;
  delete(id: string): Promise<boolean>;
  
  // Business operations
  findActiveDiscounts(validAt?: Date): Promise<Discount[]>;
  findByType(type: DiscountType): Promise<Discount[]>;
  bulkDelete(ids: string[]): Promise<number>;
  
  // Usage tracking
  incrementUsage(id: string): Promise<void>;
  recordUsage(entry: Omit<DiscountUsageEntry, 'appliedAt'>): Promise<void>;
  getUsageStats(filters?: {
    discountId?: string;
    dateFrom?: Date;
    dateTo?: Date;
  }): Promise<UsageStats>;
  
  // Search and filtering
  search(query: string, filters?: DiscountFilters): Promise<Discount[]>;
  count(filters?: DiscountFilters): Promise<number>;
  
  // Validation and utilities
  exists(id: string): Promise<boolean>;
  validateDiscount(discount: Partial<Discount>): Promise<string[]>;
  
  // Health and maintenance
  healthCheck(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    details?: Record<string, unknown>;
  }>;
}

/**
 * Configuration for the discount repository
 */
export interface DiscountRepositoryConfig {
  storageProvider?: DiscountStorageProvider;
  enableCaching?: boolean;
  cacheTimeoutMs?: number;
  enableMetrics?: boolean;
}

/**
 * Default repository implementation using storage providers
 */
export class DefaultDiscountRepository implements DiscountRepository {
  private storageProvider: DiscountStorageProvider;
  private config: DiscountRepositoryConfig;
  private metrics = {
    totalOperations: 0,
    successfulOperations: 0,
    failedOperations: 0,
    lastOperationAt: undefined as Date | undefined,
    operationTimes: [] as number[]
  };

  constructor(config: DiscountRepositoryConfig = {}) {
    this.config = {
      enableCaching: false,
      cacheTimeoutMs: 300000, // 5 minutes
      enableMetrics: true,
      ...config
    };

    // Use provided storage provider or default to in-memory
    this.storageProvider = config.storageProvider || new InMemoryDiscountStorageProvider();
    
    // Initialize storage provider if needed
    this.initializeStorage();
  }

  private async initializeStorage(): Promise<void> {
    try {
      if (this.storageProvider.initialize) {
        await this.storageProvider.initialize();
      }
      logger.info('[DiscountRepository] Storage provider initialized successfully');
    } catch (error) {
      logger.error('[DiscountRepository] Failed to initialize storage provider:', error);
      throw error;
    }
  }

  private async executeWithMetrics<T>(
    operation: string,
    fn: () => Promise<T>
  ): Promise<T> {
    const startTime = Date.now();
    
    try {
      this.metrics.totalOperations++;
      const result = await fn();
      
      this.metrics.successfulOperations++;
      this.recordOperationTime(Date.now() - startTime);
      
      logger.debug(`[DiscountRepository] ${operation} completed successfully`);
      return result;
      
    } catch (error) {
      this.metrics.failedOperations++;
      this.recordOperationTime(Date.now() - startTime);
      
      logger.error(`[DiscountRepository] ${operation} failed:`, error);
      throw error;
    }
  }

  private recordOperationTime(timeMs: number): void {
    if (this.config.enableMetrics) {
      this.metrics.operationTimes.push(timeMs);
      this.metrics.lastOperationAt = new Date();
      
      // Keep only last 100 operation times to prevent memory leaks
      if (this.metrics.operationTimes.length > 100) {
        this.metrics.operationTimes = this.metrics.operationTimes.slice(-100);
      }
    }
  }

  async create(discountData: CreateDiscountData): Promise<Discount> {
    return this.executeWithMetrics('create', async () => {
      logger.debug('[DiscountRepository] Creating new discount:', { name: discountData.name, type: discountData.type });
      
      const discount = await this.storageProvider.create(discountData);
      
      logger.info(`[DiscountRepository] Created discount: ${discount.id} (${discount.name})`);
      return discount;
    });
  }

  async findById(id: string): Promise<Discount | null> {
    return this.executeWithMetrics('findById', async () => {
      logger.debug(`[DiscountRepository] Finding discount by ID: ${id}`);
      
      const discount = await this.storageProvider.findById(id);
      
      if (discount) {
        logger.debug(`[DiscountRepository] Found discount: ${discount.name}`);
      } else {
        logger.debug(`[DiscountRepository] Discount not found: ${id}`);
      }
      
      return discount;
    });
  }

  async findAll(
    filters?: EnhancedDiscountFilters, 
    pagination?: PaginationOptions
  ): Promise<PaginatedResult<Discount>> {
    return this.executeWithMetrics('findAll', async () => {
      logger.debug('[DiscountRepository] Finding all discounts with filters:', filters);
      
      const result = await this.storageProvider.findAll(filters, pagination);
      
      logger.debug(`[DiscountRepository] Found ${result.items.length} discounts (total: ${result.total})`);
      return result;
    });
  }

  async update(id: string, updates: Partial<Discount>): Promise<Discount> {
    return this.executeWithMetrics('update', async () => {
      logger.debug(`[DiscountRepository] Updating discount: ${id}`, updates);
      
      const discount = await this.storageProvider.update(id, updates);
      
      logger.info(`[DiscountRepository] Updated discount: ${discount.id} (${discount.name})`);
      return discount;
    });
  }

  async delete(id: string): Promise<boolean> {
    return this.executeWithMetrics('delete', async () => {
      logger.debug(`[DiscountRepository] Deleting discount: ${id}`);
      
      const deleted = await this.storageProvider.delete(id);
      
      if (deleted) {
        logger.info(`[DiscountRepository] Deleted discount: ${id}`);
      } else {
        logger.warn(`[DiscountRepository] Discount not found for deletion: ${id}`);
      }
      
      return deleted;
    });
  }

  async findActiveDiscounts(validAt?: Date): Promise<Discount[]> {
    return this.executeWithMetrics('findActiveDiscounts', async () => {
      const checkDate = validAt || new Date();
      logger.debug(`[DiscountRepository] Finding active discounts at: ${checkDate.toISOString()}`);
      
      const discounts = await this.storageProvider.findActiveDiscounts(checkDate);
      
      logger.debug(`[DiscountRepository] Found ${discounts.length} active discounts`);
      return discounts;
    });
  }

  async findByType(type: DiscountType): Promise<Discount[]> {
    return this.executeWithMetrics('findByType', async () => {
      logger.debug(`[DiscountRepository] Finding discounts by type: ${type}`);
      
      const discounts = await this.storageProvider.findByType(type);
      
      logger.debug(`[DiscountRepository] Found ${discounts.length} discounts of type ${type}`);
      return discounts;
    });
  }

  async bulkDelete(ids: string[]): Promise<number> {
    return this.executeWithMetrics('bulkDelete', async () => {
      logger.debug(`[DiscountRepository] Bulk deleting ${ids.length} discounts`);
      
      const deletedCount = await this.storageProvider.bulkDelete(ids);
      
      logger.info(`[DiscountRepository] Bulk deleted ${deletedCount} discounts`);
      return deletedCount;
    });
  }

  async incrementUsage(id: string): Promise<void> {
    return this.executeWithMetrics('incrementUsage', async () => {
      logger.debug(`[DiscountRepository] Incrementing usage for discount: ${id}`);
      
      await this.storageProvider.incrementUsage(id);
      
      logger.debug(`[DiscountRepository] Incremented usage for discount: ${id}`);
    });
  }

  async recordUsage(entry: Omit<DiscountUsageEntry, 'appliedAt'>): Promise<void> {
    return this.executeWithMetrics('recordUsage', async () => {
      logger.debug(`[DiscountRepository] Recording usage for discount: ${entry.discountId}`);
      
      await this.storageProvider.recordUsage(entry);
      
      logger.debug(`[DiscountRepository] Recorded usage for discount: ${entry.discountId} (amount: ${entry.discountAmount})`);
    });
  }

  async getUsageStats(filters?: {
    discountId?: string;
    dateFrom?: Date;
    dateTo?: Date;
  }): Promise<UsageStats> {
    return this.executeWithMetrics('getUsageStats', async () => {
      logger.debug('[DiscountRepository] Getting usage statistics:', filters);
      
      const stats = await this.storageProvider.getUsageStats(filters);
      
      logger.debug(`[DiscountRepository] Retrieved usage stats: ${stats.totalApplications} applications, ${stats.totalSavings} total savings`);
      return stats;
    });
  }

  async search(query: string, filters?: DiscountFilters): Promise<Discount[]> {
    return this.executeWithMetrics('search', async () => {
      logger.debug(`[DiscountRepository] Searching discounts with query: "${query}"`);
      
      const discounts = await this.storageProvider.search(query, filters);
      
      logger.debug(`[DiscountRepository] Search returned ${discounts.length} discounts`);
      return discounts;
    });
  }

  async count(filters?: DiscountFilters): Promise<number> {
    return this.executeWithMetrics('count', async () => {
      logger.debug('[DiscountRepository] Counting discounts with filters:', filters);
      
      const count = await this.storageProvider.count(filters);
      
      logger.debug(`[DiscountRepository] Count result: ${count} discounts`);
      return count;
    });
  }

  async exists(id: string): Promise<boolean> {
    return this.executeWithMetrics('exists', async () => {
      logger.debug(`[DiscountRepository] Checking if discount exists: ${id}`);
      
      const exists = await this.storageProvider.exists(id);
      
      logger.debug(`[DiscountRepository] Discount ${id} exists: ${exists}`);
      return exists;
    });
  }

  async validateDiscount(discount: Partial<Discount>): Promise<string[]> {
    return this.executeWithMetrics('validateDiscount', async () => {
      logger.debug('[DiscountRepository] Validating discount data');
      
      const errors = await this.storageProvider.validateDiscount(discount);
      
      if (errors.length > 0) {
        logger.warn(`[DiscountRepository] Discount validation failed with ${errors.length} errors:`, errors);
      } else {
        logger.debug('[DiscountRepository] Discount validation passed');
      }
      
      return errors;
    });
  }

  async healthCheck(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    details?: Record<string, unknown>;
  }> {
    return this.executeWithMetrics('healthCheck', async () => {
      logger.debug('[DiscountRepository] Performing health check');
      
      const storageHealth = await this.storageProvider.healthCheck();
      
      // Add repository-level health metrics
      const repositoryHealth = this.getRepositoryHealth();
      
      const overallStatus = this.determineOverallHealth(storageHealth.status, repositoryHealth.status);
      
      const result = {
        status: overallStatus,
        details: {
          storage: storageHealth,
          repository: repositoryHealth,
          metrics: this.getMetrics()
        }
      };
      
      logger.debug(`[DiscountRepository] Health check completed: ${overallStatus}`);
      return result;
    });
  }

  private getRepositoryHealth(): { status: 'healthy' | 'degraded' | 'unhealthy' } {
    const successRate = this.metrics.totalOperations > 0 
      ? this.metrics.successfulOperations / this.metrics.totalOperations 
      : 1;

    if (successRate >= 0.95) {
      return { status: 'healthy' };
    } else if (successRate >= 0.8) {
      return { status: 'degraded' };
    } else {
      return { status: 'unhealthy' };
    }
  }

  private determineOverallHealth(
    storageStatus: 'healthy' | 'degraded' | 'unhealthy',
    repositoryStatus: 'healthy' | 'degraded' | 'unhealthy'
  ): 'healthy' | 'degraded' | 'unhealthy' {
    if (storageStatus === 'unhealthy' || repositoryStatus === 'unhealthy') {
      return 'unhealthy';
    }
    if (storageStatus === 'degraded' || repositoryStatus === 'degraded') {
      return 'degraded';
    }
    return 'healthy';
  }

  /**
   * Get repository performance metrics
   */
  getMetrics() {
    const averageOperationTime = this.metrics.operationTimes.length > 0
      ? this.metrics.operationTimes.reduce((sum, time) => sum + time, 0) / this.metrics.operationTimes.length
      : 0;

    return {
      totalOperations: this.metrics.totalOperations,
      successfulOperations: this.metrics.successfulOperations,
      failedOperations: this.metrics.failedOperations,
      successRate: this.metrics.totalOperations > 0 
        ? this.metrics.successfulOperations / this.metrics.totalOperations 
        : 0,
      averageOperationTimeMs: Math.round(averageOperationTime * 100) / 100,
      lastOperationAt: this.metrics.lastOperationAt
    };
  }

  /**
   * Get the underlying storage provider (for advanced operations)
   */
  getStorageProvider(): DiscountStorageProvider {
    return this.storageProvider;
  }

  /**
   * Clean up resources
   */
  async cleanup(): Promise<void> {
    try {
      if (this.storageProvider.cleanup) {
        await this.storageProvider.cleanup();
      }
      logger.info('[DiscountRepository] Cleanup completed successfully');
    } catch (error) {
      logger.error('[DiscountRepository] Cleanup failed:', error);
      throw error;
    }
  }
}

/**
 * Repository factory for creating discount repositories
 */
export class DiscountRepositoryFactory {
  static create(config?: DiscountRepositoryConfig): DiscountRepository {
    return new DefaultDiscountRepository(config);
  }

  static createWithInMemoryStorage(config?: Omit<DiscountRepositoryConfig, 'storageProvider'>): DiscountRepository {
    return new DefaultDiscountRepository({
      ...config,
      storageProvider: new InMemoryDiscountStorageProvider()
    });
  }
}

/**
 * Singleton instance for global use
 */
let defaultRepositoryInstance: DiscountRepository | null = null;

/**
 * Get the default discount repository instance
 */
export function getDefaultDiscountRepository(config?: DiscountRepositoryConfig): DiscountRepository {
  if (!defaultRepositoryInstance) {
    defaultRepositoryInstance = new DefaultDiscountRepository(config);
  }
  return defaultRepositoryInstance;
}

/**
 * Reset the default repository instance (useful for testing)
 */
export function resetDefaultDiscountRepository(): void {
  defaultRepositoryInstance = null;
}