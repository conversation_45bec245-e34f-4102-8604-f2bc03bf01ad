/**
 * Storage provider interface for discount system
 * 
 * This interface defines the contract that all storage providers must implement,
 * enabling pluggable storage backends (in-memory, database, etc.)
 */

import type { 
  Discount, 
  DiscountFilters, 
  DiscountUsageEntry,
  DiscountType 
} from '../discount.types';

// Pagination parameters for list operations
export interface PaginationOptions {
  page: number;
  limit: number;
}

// Enhanced filters with search and sorting
export interface EnhancedDiscountFilters extends DiscountFilters {
  search?: string;
  sortBy?: keyof Discount;
  sortOrder?: 'asc' | 'desc';
}

// Paginated result type
export interface PaginatedResult<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Usage statistics aggregation
export interface UsageStats {
  discountId?: string;
  totalApplications: number;
  totalSavings: number;
  averageDiscountAmount: number;
  dateRange: {
    from: Date;
    to: Date;
  };
  dailyStats?: Array<{
    date: Date;
    applications: number;
    savings: number;
  }>;
}

// Storage provider configuration
export interface StorageProviderConfig {
  name: string;
  version: string;
  options?: Record<string, unknown>;
}

// Main storage provider interface
export interface DiscountStorageProvider {
  // Provider metadata
  getConfig(): StorageProviderConfig;
  
  // CRUD operations
  create(discount: Omit<Discount, 'id' | 'createdAt' | 'updatedAt' | 'usageCount'>): Promise<Discount>;
  findById(id: string): Promise<Discount | null>;
  findAll(filters?: EnhancedDiscountFilters, pagination?: PaginationOptions): Promise<PaginatedResult<Discount>>;
  update(id: string, updates: Partial<Discount>): Promise<Discount>;
  delete(id: string): Promise<boolean>;
  
  // Bulk operations
  findActiveDiscounts(validAt?: Date): Promise<Discount[]>;
  findByType(type: DiscountType): Promise<Discount[]>;
  bulkDelete(ids: string[]): Promise<number>; // Returns count of deleted items
  
  // Usage tracking
  incrementUsage(id: string): Promise<void>;
  recordUsage(entry: Omit<DiscountUsageEntry, 'appliedAt'>): Promise<void>;
  getUsageStats(filters?: {
    discountId?: string;
    dateFrom?: Date;
    dateTo?: Date;
  }): Promise<UsageStats>;
  
  // Search and filtering
  search(query: string, filters?: DiscountFilters): Promise<Discount[]>;
  count(filters?: DiscountFilters): Promise<number>;
  
  // Validation and health checks
  exists(id: string): Promise<boolean>;
  validateDiscount(discount: Partial<Discount>): Promise<string[]>; // Returns validation errors
  healthCheck(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    details?: Record<string, unknown>;
  }>;
  
  // Storage provider lifecycle
  initialize?(): Promise<void>;
  cleanup?(): Promise<void>;
  
  // Transaction support (optional for providers that support it)
  beginTransaction?(): Promise<StorageTransaction>;
}

// Transaction interface for providers that support transactions
export interface StorageTransaction {
  commit(): Promise<void>;
  rollback(): Promise<void>;
  
  // Same CRUD operations but within transaction context
  create(discount: Omit<Discount, 'id' | 'createdAt' | 'updatedAt' | 'usageCount'>): Promise<Discount>;
  update(id: string, updates: Partial<Discount>): Promise<Discount>;
  delete(id: string): Promise<boolean>;
}

// Storage provider factory interface
export interface StorageProviderFactory {
  createProvider(config: StorageProviderConfig): Promise<DiscountStorageProvider>;
  getSupportedProviders(): string[];
}