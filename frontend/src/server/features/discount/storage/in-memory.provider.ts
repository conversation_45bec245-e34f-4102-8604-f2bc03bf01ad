/**
 * In-memory storage provider for discount system
 * 
 * This provider implements the DiscountStorageProvider interface using
 * in-memory Map-based storage with thread-safe operations for concurrent access.
 */

import type { 
  Discount, 
  DiscountUsageEntry,
  DiscountType 
} from '../discount.types';
import type { 
  DiscountStorageProvider,
  EnhancedDiscountFilters,
  PaginationOptions,
  PaginatedResult,
  UsageStats,
  StorageProviderConfig,
  StorageTransaction
} from './storage.interface';
import { StorageUtils } from './storage.utils';
import { 
  StorageError, 
  StorageValidationError, 
  StorageConstraintError,
  StorageTransactionError 
} from './storage.errors';
import { DiscountNotFoundError } from '../../../shared/utils/errors';

// In-memory storage structure
interface InMemoryStore {
  discounts: Map<string, Discount>;
  usageEntries: DiscountUsageEntry[];
  initialized: boolean;
  transactionCounter: number;
}

// Transaction context for in-memory operations
class InMemoryTransaction implements StorageTransaction {
  private operations: Array<() => void> = [];
  private rollbackOperations: Array<() => void> = [];
  private committed = false;
  private rolledBack = false;

  constructor(
    private store: InMemoryStore,
    private transactionId: string
  ) {}

  async create(discount: Omit<Discount, 'id' | 'createdAt' | 'updatedAt' | 'usageCount'>): Promise<Discount> {
    if (this.committed || this.rolledBack) {
      throw new StorageTransactionError('create', 'Transaction already completed');
    }

    const id = StorageUtils.generateId();
    const now = StorageUtils.getCurrentTimestamp();
    const newDiscount: Discount = {
      ...StorageUtils.sanitizeDiscountInput(discount),
      id,
      createdAt: now,
      updatedAt: now,
      usageCount: 0
    };

    // Validate the discount
    const validationErrors = StorageUtils.validateDiscountData(newDiscount);
    if (validationErrors.length > 0) {
      throw new StorageValidationError('discount', newDiscount, validationErrors.join(', '));
    }

    // Add to transaction operations
    this.operations.push(() => {
      this.store.discounts.set(id, newDiscount);
    });

    this.rollbackOperations.push(() => {
      this.store.discounts.delete(id);
    });

    return StorageUtils.cloneDiscount(newDiscount);
  }

  async update(id: string, updates: Partial<Discount>): Promise<Discount> {
    if (this.committed || this.rolledBack) {
      throw new StorageTransactionError('update', 'Transaction already completed');
    }

    const existingDiscount = this.store.discounts.get(id);
    if (!existingDiscount) {
      throw new DiscountNotFoundError(id);
    }

    const updatedDiscount: Discount = {
      ...existingDiscount,
      ...updates,
      id, // Ensure ID cannot be changed
      updatedAt: StorageUtils.getCurrentTimestamp()
    };

    // Validate the updated discount
    const validationErrors = StorageUtils.validateDiscountData(updatedDiscount);
    if (validationErrors.length > 0) {
      throw new StorageValidationError('discount', updatedDiscount, validationErrors.join(', '));
    }

    const originalDiscount = StorageUtils.cloneDiscount(existingDiscount);

    // Add to transaction operations
    this.operations.push(() => {
      this.store.discounts.set(id, updatedDiscount);
    });

    this.rollbackOperations.push(() => {
      this.store.discounts.set(id, originalDiscount);
    });

    return StorageUtils.cloneDiscount(updatedDiscount);
  }

  async delete(id: string): Promise<boolean> {
    if (this.committed || this.rolledBack) {
      throw new StorageTransactionError('delete', 'Transaction already completed');
    }

    const existingDiscount = this.store.discounts.get(id);
    if (!existingDiscount) {
      return false;
    }

    const backupDiscount = StorageUtils.cloneDiscount(existingDiscount);

    // Add to transaction operations
    this.operations.push(() => {
      this.store.discounts.delete(id);
    });

    this.rollbackOperations.push(() => {
      this.store.discounts.set(id, backupDiscount);
    });

    return true;
  }

  async commit(): Promise<void> {
    if (this.committed || this.rolledBack) {
      throw new StorageTransactionError('commit', 'Transaction already completed');
    }

    try {
      // Execute all operations
      for (const operation of this.operations) {
        operation();
      }
      this.committed = true;
    } catch (error) {
      // If any operation fails, rollback
      await this.rollback();
      throw new StorageTransactionError('commit', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  async rollback(): Promise<void> {
    if (this.committed || this.rolledBack) {
      throw new StorageTransactionError('rollback', 'Transaction already completed');
    }

    try {
      // Execute rollback operations in reverse order
      for (let i = this.rollbackOperations.length - 1; i >= 0; i--) {
        this.rollbackOperations[i]();
      }
      this.rolledBack = true;
    } catch (error) {
      throw new StorageTransactionError('rollback', error instanceof Error ? error.message : 'Unknown error');
    }
  }
}

export class InMemoryDiscountStorageProvider implements DiscountStorageProvider {
  private store: InMemoryStore = {
    discounts: new Map(),
    usageEntries: [],
    initialized: false,
    transactionCounter: 0
  };

  private config: StorageProviderConfig = {
    name: 'in-memory',
    version: '1.0.0',
    options: {
      maxDiscounts: 10000,
      maxUsageEntries: 100000
    }
  };

  getConfig(): StorageProviderConfig {
    return { ...this.config };
  }

  async initialize(): Promise<void> {
    if (this.store.initialized) {
      return;
    }

    // Initialize with empty storage
    this.store.discounts.clear();
    this.store.usageEntries.length = 0;
    this.store.initialized = true;
  }

  async cleanup(): Promise<void> {
    this.store.discounts.clear();
    this.store.usageEntries.length = 0;
    this.store.initialized = false;
  }

  async create(discount: Omit<Discount, 'id' | 'createdAt' | 'updatedAt' | 'usageCount'>): Promise<Discount> {
    const id = StorageUtils.generateId();
    const now = StorageUtils.getCurrentTimestamp();
    const newDiscount: Discount = {
      ...StorageUtils.sanitizeDiscountInput(discount),
      id,
      createdAt: now,
      updatedAt: now,
      usageCount: 0
    };

    // Validate the discount
    const validationErrors = StorageUtils.validateDiscountData(newDiscount);
    if (validationErrors.length > 0) {
      throw new StorageValidationError('discount', newDiscount, validationErrors.join(', '));
    }

    // Check storage limits
    const maxDiscounts = this.config.options?.maxDiscounts as number || 10000;
    if (this.store.discounts.size >= maxDiscounts) {
      throw new StorageConstraintError('max_discounts', `Maximum of ${maxDiscounts} discounts allowed`);
    }

    this.store.discounts.set(id, newDiscount);
    return StorageUtils.cloneDiscount(newDiscount);
  }

  async findById(id: string): Promise<Discount | null> {
    const discount = this.store.discounts.get(id);
    return discount ? StorageUtils.cloneDiscount(discount) : null;
  }

  async findAll(
    filters?: EnhancedDiscountFilters, 
    pagination?: PaginationOptions
  ): Promise<PaginatedResult<Discount>> {
    // Get all discounts as array
    let discounts = Array.from(this.store.discounts.values());

    // Apply filters
    discounts = StorageUtils.applyFilters(discounts, filters);

    // Apply sorting
    discounts = StorageUtils.applySorting(discounts, filters?.sortBy, filters?.sortOrder);

    // Apply pagination
    return StorageUtils.applyPagination(discounts, pagination);
  }

  async update(id: string, updates: Partial<Discount>): Promise<Discount> {
    const existingDiscount = this.store.discounts.get(id);
    if (!existingDiscount) {
      throw new DiscountNotFoundError(id);
    }

    const updatedDiscount: Discount = {
      ...existingDiscount,
      ...updates,
      id, // Ensure ID cannot be changed
      updatedAt: StorageUtils.getCurrentTimestamp()
    };

    // Validate the updated discount
    const validationErrors = StorageUtils.validateDiscountData(updatedDiscount);
    if (validationErrors.length > 0) {
      throw new StorageValidationError('discount', updatedDiscount, validationErrors.join(', '));
    }

    this.store.discounts.set(id, updatedDiscount);
    return StorageUtils.cloneDiscount(updatedDiscount);
  }

  async delete(id: string): Promise<boolean> {
    const exists = this.store.discounts.has(id);
    if (exists) {
      this.store.discounts.delete(id);
      // Also remove related usage entries
      this.store.usageEntries = this.store.usageEntries.filter(entry => entry.discountId !== id);
    }
    return exists;
  }

  async findActiveDiscounts(validAt: Date = new Date()): Promise<Discount[]> {
    const allDiscounts = Array.from(this.store.discounts.values());
    const activeDiscounts = StorageUtils.filterActiveDiscounts(allDiscounts, validAt);
    return activeDiscounts.map(discount => StorageUtils.cloneDiscount(discount));
  }

  async findByType(type: DiscountType): Promise<Discount[]> {
    const discounts = Array.from(this.store.discounts.values())
      .filter(discount => discount.type === type);
    return discounts.map(discount => StorageUtils.cloneDiscount(discount));
  }

  async bulkDelete(ids: string[]): Promise<number> {
    let deletedCount = 0;
    for (const id of ids) {
      if (this.store.discounts.has(id)) {
        this.store.discounts.delete(id);
        deletedCount++;
      }
    }

    // Remove related usage entries
    this.store.usageEntries = this.store.usageEntries.filter(
      entry => !ids.includes(entry.discountId)
    );

    return deletedCount;
  }

  async incrementUsage(id: string): Promise<void> {
    const discount = this.store.discounts.get(id);
    if (!discount) {
      throw new DiscountNotFoundError(id);
    }

    const updatedDiscount: Discount = {
      ...discount,
      usageCount: discount.usageCount + 1,
      updatedAt: StorageUtils.getCurrentTimestamp()
    };

    this.store.discounts.set(id, updatedDiscount);
  }

  async recordUsage(entry: Omit<DiscountUsageEntry, 'appliedAt'>): Promise<void> {
    const usageEntry: DiscountUsageEntry = {
      ...entry,
      appliedAt: StorageUtils.getCurrentTimestamp()
    };

    // Check storage limits
    const maxUsageEntries = this.config.options?.maxUsageEntries as number || 100000;
    if (this.store.usageEntries.length >= maxUsageEntries) {
      // Remove oldest entries to make room
      const removeCount = Math.floor(maxUsageEntries * 0.1); // Remove 10%
      this.store.usageEntries.splice(0, removeCount);
    }

    this.store.usageEntries.push(usageEntry);
    await this.incrementUsage(entry.discountId);
  }

  async getUsageStats(filters?: {
    discountId?: string;
    dateFrom?: Date;
    dateTo?: Date;
  }): Promise<UsageStats> {
    let entries = this.store.usageEntries;

    // Apply filters
    if (filters?.discountId) {
      entries = entries.filter(entry => entry.discountId === filters.discountId);
    }

    if (filters?.dateFrom) {
      entries = entries.filter(entry => entry.appliedAt >= filters.dateFrom!);
    }

    if (filters?.dateTo) {
      entries = entries.filter(entry => entry.appliedAt <= filters.dateTo!);
    }

    // Calculate statistics
    const totalApplications = entries.length;
    const totalSavings = entries.reduce((sum, entry) => sum + entry.discountAmount, 0);
    const averageDiscountAmount = totalApplications > 0 ? totalSavings / totalApplications : 0;

    // Determine date range
    const now = new Date();
    const dateRange = {
      from: filters?.dateFrom || new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
      to: filters?.dateTo || now
    };

    return {
      discountId: filters?.discountId,
      totalApplications,
      totalSavings,
      averageDiscountAmount,
      dateRange
    };
  }

  async search(query: string, filters?: EnhancedDiscountFilters): Promise<Discount[]> {
    const searchFilters: EnhancedDiscountFilters = {
      ...filters,
      search: query
    };

    const result = await this.findAll(searchFilters);
    return result.items;
  }

  async count(filters?: EnhancedDiscountFilters): Promise<number> {
    const discounts = Array.from(this.store.discounts.values());
    const filtered = StorageUtils.applyFilters(discounts, filters);
    return filtered.length;
  }

  async exists(id: string): Promise<boolean> {
    return this.store.discounts.has(id);
  }

  async validateDiscount(discount: Partial<Discount>): Promise<string[]> {
    return StorageUtils.validateDiscountData(discount);
  }

  async healthCheck(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    details?: Record<string, unknown>;
  }> {
    const discountCount = this.store.discounts.size;
    const usageEntryCount = this.store.usageEntries.length;
    const maxDiscounts = this.config.options?.maxDiscounts as number || 10000;
    const maxUsageEntries = this.config.options?.maxUsageEntries as number || 100000;

    const discountUsageRatio = discountCount / maxDiscounts;
    const usageEntryRatio = usageEntryCount / maxUsageEntries;

    let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';

    if (discountUsageRatio > 0.9 || usageEntryRatio > 0.9) {
      status = 'degraded';
    }

    if (discountUsageRatio >= 1 || usageEntryRatio >= 1) {
      status = 'unhealthy';
    }

    return {
      status,
      details: {
        initialized: this.store.initialized,
        discountCount,
        usageEntryCount,
        maxDiscounts,
        maxUsageEntries,
        discountUsageRatio: Math.round(discountUsageRatio * 100) / 100,
        usageEntryRatio: Math.round(usageEntryRatio * 100) / 100
      }
    };
  }

  async beginTransaction(): Promise<StorageTransaction> {
    const transactionId = `tx_${++this.store.transactionCounter}_${Date.now()}`;
    return new InMemoryTransaction(this.store, transactionId);
  }
}