/**
 * Zod validation schemas for discount system
 * 
 * This file contains comprehensive Zod schemas for runtime type validation
 * and seamless tRPC integration throughout the discount system.
 */

import { z } from 'zod';

// Base discount schema with common fields (without refinements for extensibility)
const BaseDiscountSchemaCore = z.object({
    name: z.string()
        .min(1, 'Name is required')
        .max(100, 'Name cannot exceed 100 characters')
        .transform(val => val.trim().replace(/\s+/g, ' ')), // Sanitize name
    description: z.string()
        .max(500, 'Description cannot exceed 500 characters')
        .transform(val => val.trim().replace(/\s+/g, ' '))
        .optional(),
    isActive: z.boolean().default(true),
    validFrom: z.coerce.date(),
    validTo: z.coerce.date(),
    maxUsage: z.number()
        .int('Maximum usage must be an integer')
        .min(1, 'Maximum usage must be at least 1')
        .optional()
});

// Base discount schema with date validation
export const BaseDiscountSchema = BaseDiscountSchemaCore.refine(
    (data: z.infer<typeof BaseDiscountSchemaCore>) => data.validFrom < data.validTo,
    {
        message: 'Valid from date must be before valid to date',
        path: ['validTo']
    }
).refine(
    (data: z.infer<typeof BaseDiscountSchemaCore>) => data.validTo > new Date(),
    {
        message: 'Valid to date must be in the future',
        path: ['validTo']
    }
);

// Percentage cap discount schema (built from core schema to avoid extend() issues)
const PercentageCapDiscountSchemaCore = BaseDiscountSchemaCore.extend({
    type: z.literal('PERCENTAGE_CAP'),
    percentage: z.number()
        .min(0.01, 'Percentage must be greater than 0')
        .max(100, 'Percentage cannot exceed 100'),
    maxDiscountAmount: z.number()
        .min(0, 'Maximum discount amount cannot be negative'),
    minCartValue: z.number()
        .min(0, 'Minimum cart value cannot be negative')
});

export const PercentageCapDiscountSchema = PercentageCapDiscountSchemaCore
    .refine(
        (data: z.infer<typeof PercentageCapDiscountSchemaCore>) => data.validFrom < data.validTo,
        {
            message: 'Valid from date must be before valid to date',
            path: ['validTo']
        }
    )
    .refine(
        (data: z.infer<typeof PercentageCapDiscountSchemaCore>) => data.validTo > new Date(),
        {
            message: 'Valid to date must be in the future',
            path: ['validTo']
        }
    )
    .refine(
        (data: z.infer<typeof PercentageCapDiscountSchemaCore>) => !(data.maxDiscountAmount === 0 && data.percentage > 0),
        {
            message: 'Maximum discount amount cannot be zero when percentage is greater than zero',
            path: ['maxDiscountAmount']
        }
    );

// Create discount schema (currently only supports PERCENTAGE_CAP, will be union when more types are added)
export const CreateDiscountSchema = PercentageCapDiscountSchema;

// Update schema (manual partial to avoid issues with discriminated union)
export const UpdateDiscountSchema = z.object({
    id: z.string().uuid('Invalid discount ID format'),
    name: z.string()
        .min(1, 'Name is required')
        .max(100, 'Name cannot exceed 100 characters')
        .transform(val => val.trim().replace(/\s+/g, ' '))
        .optional(),
    description: z.string()
        .max(500, 'Description cannot exceed 500 characters')
        .transform(val => val.trim().replace(/\s+/g, ' '))
        .optional(),
    type: z.literal('PERCENTAGE_CAP').optional(),
    isActive: z.boolean().optional(),
    validFrom: z.coerce.date().optional(),
    validTo: z.coerce.date().optional(),
    maxUsage: z.number()
        .int('Maximum usage must be an integer')
        .min(1, 'Maximum usage must be at least 1')
        .optional(),
    percentage: z.number()
        .min(0.01, 'Percentage must be greater than 0')
        .max(100, 'Percentage cannot exceed 100')
        .optional(),
    maxDiscountAmount: z.number()
        .min(0, 'Maximum discount amount cannot be negative')
        .optional(),
    minCartValue: z.number()
        .min(0, 'Minimum cart value cannot be negative')
        .optional()
}).refine(
    (data: {
        validFrom?: Date;
        validTo?: Date;
        maxDiscountAmount?: number;
        percentage?: number;
    }) => {
        // If both dates are provided, validate the range
        if (data.validFrom && data.validTo) {
            return data.validFrom < data.validTo;
        }
        return true;
    },
    {
        message: 'Valid from date must be before valid to date',
        path: ['validTo']
    }
).refine(
    (data: {
        maxDiscountAmount?: number;
        percentage?: number;
    }) => {
        // If both percentage and max discount are provided, validate business rule
        if (data.percentage !== undefined && data.maxDiscountAmount !== undefined) {
            return !(data.maxDiscountAmount === 0 && data.percentage > 0);
        }
        return true;
    },
    {
        message: 'Maximum discount amount cannot be zero when percentage is greater than zero',
        path: ['maxDiscountAmount']
    }
);

// Cart item schema for discount calculations
export const CartItemSchema = z.object({
    skuId: z.number()
        .int('SKU ID must be an integer')
        .positive('SKU ID must be a positive integer'),
    variantSkuId: z.number()
        .int('Variant SKU ID must be an integer')
        .positive('Variant SKU ID must be a positive integer')
        .optional(),
    quantity: z.number()
        .int('Quantity must be an integer')
        .min(1, 'Quantity must be at least 1'),
    pricePerUnit: z.number()
        .min(0, 'Price per unit cannot be negative'),
    mrpPerUnit: z.number()
        .min(0, 'MRP per unit cannot be negative'),
    name: z.string().optional(),
    categoryId: z.number()
        .int('Category ID must be an integer')
        .positive('Category ID must be a positive integer')
        .optional()
});

// Cart calculation schema
export const CalculateDiscountSchema = z.object({
    cartItems: z.array(CartItemSchema)
        .min(1, 'Cart must contain at least one item')
        .max(100, 'Cart cannot contain more than 100 items') // Reasonable limit
});

// Query schemas
export const GetDiscountByIdSchema = z.object({
    id: z.string().uuid('Invalid discount ID format')
});

export const DeleteDiscountSchema = z.object({
    id: z.string().uuid('Invalid discount ID format')
});

// List discounts schema with pagination and filtering
export const ListDiscountsSchema = z.object({
    page: z.number()
        .int('Page must be an integer')
        .min(1, 'Page must be at least 1')
        .default(1),
    limit: z.number()
        .int('Limit must be an integer')
        .min(1, 'Limit must be at least 1')
        .max(100, 'Limit cannot exceed 100')
        .default(20),
    filters: z.object({
        isActive: z.boolean().optional(),
        type: z.enum(['PERCENTAGE_CAP']).optional(),
        validAt: z.coerce.date().optional(),
        search: z.string()
            .min(1, 'Search term must be at least 1 character')
            .max(100, 'Search term cannot exceed 100 characters')
            .optional()
    }).optional()
});

// Usage statistics schema
export const GetUsageStatsSchema = z.object({
    discountId: z.string().uuid('Invalid discount ID format').optional(),
    dateFrom: z.coerce.date().optional(),
    dateTo: z.coerce.date().optional()
}).refine(
    (data: {
        dateFrom?: Date;
        dateTo?: Date;
    }) => {
        // If both dates are provided, validate the range
        if (data.dateFrom && data.dateTo) {
            return data.dateFrom <= data.dateTo;
        }
        return true;
    },
    {
        message: 'Date from must be before or equal to date to',
        path: ['dateTo']
    }
);

// Response schemas for type inference
export const DiscountResponseSchema = z.object({
    id: z.string().uuid(),
    name: z.string(),
    description: z.string().optional(),
    type: z.enum(['PERCENTAGE_CAP']),
    isActive: z.boolean(),
    validFrom: z.date(),
    validTo: z.date(),
    createdAt: z.date(),
    updatedAt: z.date(),
    usageCount: z.number().int().min(0),
    maxUsage: z.number().int().min(1).optional(),
    // Type-specific fields (will be present based on discriminated union)
    percentage: z.number().min(0.01).max(100).optional(),
    maxDiscountAmount: z.number().min(0).optional(),
    minCartValue: z.number().min(0).optional()
});

export const AppliedDiscountSchema = z.object({
    discountId: z.string().uuid(),
    discountName: z.string(),
    discountAmount: z.number().min(0),
    discountType: z.enum(['PERCENTAGE_CAP'])
});

export const DiscountCalculationResultSchema = z.object({
    totalDiscount: z.number().min(0),
    appliedDiscounts: z.array(AppliedDiscountSchema),
    originalTotal: z.number().min(0),
    finalTotal: z.number().min(0),
    savings: z.number().min(0)
}).refine(
    (data: {
        totalDiscount: number;
        originalTotal: number;
        finalTotal: number;
    }) => data.finalTotal === data.originalTotal - data.totalDiscount,
    {
        message: 'Final total must equal original total minus total discount',
        path: ['finalTotal']
    }
).refine(
    (data: {
        totalDiscount: number;
        savings: number;
    }) => data.savings === data.totalDiscount,
    {
        message: 'Savings must equal total discount',
        path: ['savings']
    }
);

export const PaginatedDiscountsResponseSchema = z.object({
    items: z.array(DiscountResponseSchema),
    total: z.number().int().min(0),
    page: z.number().int().min(1),
    limit: z.number().int().min(1),
    totalPages: z.number().int().min(0)
});

export const UsageStatsResponseSchema = z.object({
    discountId: z.string().uuid().optional(),
    totalApplications: z.number().int().min(0),
    totalSavings: z.number().min(0),
    averageDiscountAmount: z.number().min(0),
    dateRange: z.object({
        from: z.date(),
        to: z.date()
    }),
    dailyStats: z.array(z.object({
        date: z.date(),
        applications: z.number().int().min(0),
        savings: z.number().min(0)
    })).optional()
});

// Type inference exports for use throughout the application
export type CreateDiscountInput = z.infer<typeof CreateDiscountSchema>;
export type UpdateDiscountInput = z.infer<typeof UpdateDiscountSchema>;
export type CalculateDiscountInput = z.infer<typeof CalculateDiscountSchema>;
export type GetDiscountByIdInput = z.infer<typeof GetDiscountByIdSchema>;
export type ListDiscountsInput = z.infer<typeof ListDiscountsSchema>;
export type GetUsageStatsInput = z.infer<typeof GetUsageStatsSchema>;

export type DiscountResponse = z.infer<typeof DiscountResponseSchema>;
export type AppliedDiscount = z.infer<typeof AppliedDiscountSchema>;
export type DiscountCalculationResult = z.infer<typeof DiscountCalculationResultSchema>;
export type PaginatedDiscountsResponse = z.infer<typeof PaginatedDiscountsResponseSchema>;
export type UsageStatsResponse = z.infer<typeof UsageStatsResponseSchema>;
export type CartItem = z.infer<typeof CartItemSchema>;

// Validation helper functions
export const ValidationHelpers = {
    /**
     * Validate and parse discount creation input
     */
    parseCreateDiscountInput: (input: unknown) => {
        return CreateDiscountSchema.parse(input);
    },

    /**
     * Validate and parse discount update input
     */
    parseUpdateDiscountInput: (input: unknown) => {
        return UpdateDiscountSchema.parse(input);
    },

    /**
     * Validate and parse cart items for discount calculation
     */
    parseCalculateDiscountInput: (input: unknown) => {
        return CalculateDiscountSchema.parse(input);
    },

    /**
     * Safe parse with error handling
     */
    safeParseCreateDiscount: (input: unknown) => {
        return CreateDiscountSchema.safeParse(input);
    },

    /**
     * Get validation error messages in a user-friendly format
     */
    getValidationErrors: (error: z.ZodError) => {
        return error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message
        }));
    }
};