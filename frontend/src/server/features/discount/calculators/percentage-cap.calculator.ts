/**
 * Percentage Cap Discount Calculator
 * 
 * Implements the "X% off up to Y on cart value above Z" discount type.
 * This calculator applies a percentage discount with a maximum cap amount
 * when the cart value meets the minimum threshold.
 */

import type { DiscountCalculator } from '../discount.engine.interface';
import type { 
  Discount, 
  CartItemWithDetails, 
  DiscountType,
  PercentageCapDiscount 
} from '../discount.types';
import { DiscountTypeUtils } from '../discount.types';
import { DiscountEngineUtils } from '../discount.engine.interface';
import { logger } from '@/lib/logger';

/**
 * Calculator for percentage cap discounts
 * 
 * This calculator handles discounts of the format:
 * "X% off up to Y on cart value above Z"
 * 
 * Example: "10% off up to ₹50 on cart value above ₹300"
 * - If cart total is ₹500, discount = min(₹50, ₹500 * 0.10) = ₹50
 * - If cart total is ₹200, discount = ₹0 (below minimum threshold)
 */
export class PercentageCapDiscountCalculator implements DiscountCalculator {
  readonly type: DiscountType = 'PERCENTAGE_CAP';

  /**
   * Check if this discount can be applied to the given cart
   * 
   * Requirements for application:
   * 1. Discount must be active
   * 2. Current date must be within validity period
   * 3. Usage limit must not be exceeded
   * 4. Cart total must meet minimum value threshold
   * 
   * @param discount - The percentage cap discount to evaluate
   * @param cartItems - The cart items to check against
   * @returns true if the discount can be applied, false otherwise
   */
  canApply(discount: Discount, cartItems: CartItemWithDetails[]): boolean {
    try {
      // Validate discount type
      if (!DiscountTypeUtils.isPercentageCapDiscount(discount)) {
        logger.warn(`[PercentageCapCalculator] Invalid discount type: ${(discount as any).type || 'unknown'}`);
        return false;
      }

      // Check if discount can be applied based on business rules
      if (!DiscountTypeUtils.canBeApplied(discount)) {
        return false;
      }

      // Calculate cart total
      const cartTotal = DiscountEngineUtils.calculateCartTotal(cartItems);
      
      // Check minimum cart value threshold
      if (cartTotal < discount.minCartValue) {
        return false;
      }

      return true;

    } catch (error) {
      logger.error(`[PercentageCapCalculator] Error in canApply for discount ${discount?.id || 'unknown'}:`, error);
      return false;
    }
  }

  /**
   * Calculate the discount amount for the given cart
   * 
   * Calculation logic:
   * 1. Calculate percentage discount: cartTotal * (percentage / 100)
   * 2. Apply maximum cap: min(calculatedDiscount, maxDiscountAmount)
   * 3. Ensure non-negative result
   * 
   * @param discount - The percentage cap discount to apply
   * @param cartItems - The cart items to calculate discount for
   * @returns The calculated discount amount (always positive or zero)
   */
  calculate(discount: Discount, cartItems: CartItemWithDetails[]): number {
    try {
      // Validate discount type
      if (!DiscountTypeUtils.isPercentageCapDiscount(discount)) {
        logger.error(`[PercentageCapCalculator] Invalid discount type for calculation: ${(discount as any).type || 'unknown'}`);
        return 0;
      }

      // Check if discount can be applied
      if (!this.canApply(discount, cartItems)) {
        return 0;
      }

      // At this point, we know discount is PercentageCapDiscount
      const percentageDiscount = discount;
      const cartTotal = DiscountEngineUtils.calculateCartTotal(cartItems);

      // Calculate percentage-based discount
      const percentageAmount = cartTotal * (percentageDiscount.percentage / 100);

      // Apply maximum discount cap
      const discountAmount = Math.min(percentageAmount, percentageDiscount.maxDiscountAmount);

      // Ensure non-negative result
      const finalDiscount = Math.max(0, discountAmount);

      logger.debug(`[PercentageCapCalculator] Calculated discount for ${discount.id}:`, {
        cartTotal,
        percentage: percentageDiscount.percentage,
        percentageAmount,
        maxCap: percentageDiscount.maxDiscountAmount,
        finalDiscount
      });

      return Math.round(finalDiscount * 100) / 100; // Round to 2 decimal places

    } catch (error) {
      logger.error(`[PercentageCapCalculator] Error calculating discount ${discount?.id || 'unknown'}:`, error);
      return 0;
    }
  }

  /**
   * Validate the discount configuration for this calculator
   * 
   * Validation rules:
   * 1. Percentage must be between 0.01 and 100
   * 2. Maximum discount amount must be positive
   * 3. Minimum cart value must be non-negative
   * 4. Maximum discount should be reasonable relative to percentage
   * 
   * @param discount - The discount to validate
   * @returns Array of validation error messages (empty if valid)
   */
  validate(discount: Discount): string[] {
    const errors: string[] = [];

    try {
      // Validate discount type
      if (!DiscountTypeUtils.isPercentageCapDiscount(discount)) {
        errors.push(`Invalid discount type: expected PERCENTAGE_CAP, got ${(discount as any).type || 'unknown'}`);
        return errors;
      }

      const percentageDiscount = discount as PercentageCapDiscount;

      // Validate percentage
      if (typeof percentageDiscount.percentage !== 'number') {
        errors.push('Percentage must be a number');
      } else {
        if (percentageDiscount.percentage <= 0) {
          errors.push('Percentage must be greater than 0');
        }
        if (percentageDiscount.percentage > 100) {
          errors.push('Percentage cannot exceed 100');
        }
        if (percentageDiscount.percentage < 0.01) {
          errors.push('Percentage must be at least 0.01 (0.01%)');
        }
      }

      // Validate maximum discount amount
      if (typeof percentageDiscount.maxDiscountAmount !== 'number') {
        errors.push('Maximum discount amount must be a number');
      } else {
        if (percentageDiscount.maxDiscountAmount <= 0) {
          errors.push('Maximum discount amount must be greater than 0');
        }
        if (percentageDiscount.maxDiscountAmount > 100000) {
          errors.push('Maximum discount amount seems unreasonably high (> ₹100,000)');
        }
      }

      // Validate minimum cart value
      if (typeof percentageDiscount.minCartValue !== 'number') {
        errors.push('Minimum cart value must be a number');
      } else {
        if (percentageDiscount.minCartValue < 0) {
          errors.push('Minimum cart value cannot be negative');
        }
        if (percentageDiscount.minCartValue > 1000000) {
          errors.push('Minimum cart value seems unreasonably high (> ₹10,00,000)');
        }
      }

      // Cross-field validation
      if (typeof percentageDiscount.percentage === 'number' && 
          typeof percentageDiscount.maxDiscountAmount === 'number' &&
          typeof percentageDiscount.minCartValue === 'number') {
        
        // Check if max discount is achievable with the percentage
        const maxPossibleDiscount = percentageDiscount.minCartValue * (percentageDiscount.percentage / 100);
        if (percentageDiscount.maxDiscountAmount > maxPossibleDiscount * 10) {
          errors.push('Maximum discount amount is too high relative to percentage and minimum cart value');
        }

        // Check if minimum cart value makes sense with max discount
        if (percentageDiscount.minCartValue > 0 && percentageDiscount.maxDiscountAmount > percentageDiscount.minCartValue) {
          errors.push('Maximum discount amount should not exceed minimum cart value');
        }
      }

      // Validate common discount fields
      if (!discount.name || discount.name.trim().length === 0) {
        errors.push('Discount name is required');
      }

      if (discount.validFrom >= discount.validTo) {
        errors.push('Valid from date must be before valid to date');
      }

      if (discount.maxUsage !== undefined && discount.maxUsage < 1) {
        errors.push('Maximum usage must be at least 1 if specified');
      }

    } catch (error) {
      logger.error(`[PercentageCapCalculator] Error validating discount ${discount?.id || 'unknown'}:`, error);
      errors.push('Validation failed due to unexpected error');
    }

    return errors;
  }

  /**
   * Get a human-readable description of how this discount works
   * 
   * @param discount - The discount to describe
   * @returns A description string
   */
  getDescription(discount: Discount): string {
    if (!DiscountTypeUtils.isPercentageCapDiscount(discount)) {
      return 'Invalid percentage cap discount';
    }

    const percentageDiscount = discount as PercentageCapDiscount;
    return `${percentageDiscount.percentage}% off up to ₹${percentageDiscount.maxDiscountAmount} on cart value above ₹${percentageDiscount.minCartValue}`;
  }

  /**
   * Calculate the maximum possible discount for this configuration
   * 
   * @param discount - The discount configuration
   * @returns The maximum possible discount amount
   */
  getMaxPossibleDiscount(discount: Discount): number {
    if (!DiscountTypeUtils.isPercentageCapDiscount(discount)) {
      return 0;
    }

    const percentageDiscount = discount as PercentageCapDiscount;
    return percentageDiscount.maxDiscountAmount;
  }

  /**
   * Calculate what cart value would be needed to get the maximum discount
   * 
   * @param discount - The discount configuration
   * @returns The cart value needed for maximum discount
   */
  getCartValueForMaxDiscount(discount: Discount): number {
    if (!DiscountTypeUtils.isPercentageCapDiscount(discount)) {
      return 0;
    }

    const percentageDiscount = discount as PercentageCapDiscount;
    const cartValueForMaxDiscount = percentageDiscount.maxDiscountAmount / (percentageDiscount.percentage / 100);
    
    return Math.max(cartValueForMaxDiscount, percentageDiscount.minCartValue);
  }

  /**
   * Check if a cart would receive the maximum possible discount
   * 
   * @param discount - The discount configuration
   * @param cartItems - The cart items to check
   * @returns true if cart would get maximum discount
   */
  wouldReceiveMaxDiscount(discount: Discount, cartItems: CartItemWithDetails[]): boolean {
    if (!this.canApply(discount, cartItems)) {
      return false;
    }

    const cartTotal = DiscountEngineUtils.calculateCartTotal(cartItems);
    const requiredCartValue = this.getCartValueForMaxDiscount(discount);
    
    return cartTotal >= requiredCartValue;
  }
}