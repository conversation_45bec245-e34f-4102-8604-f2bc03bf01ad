/**
 * Shared validation helpers for the discount system
 * 
 * This file contains reusable validation functions to eliminate code duplication
 * and provide consistent validation logic across the discount system.
 */

import { z } from 'zod';
import { getValidationConfig } from './discount.config';

/**
 * Get validation configuration
 */
const getValidationLimits = () => getValidationConfig();

/**
 * Reusable validation refinement functions
 */
export const ValidationRefinements = {
  /**
   * Date range validation refinement
   */
  dateRange: <T extends { validFrom?: Date; validTo?: Date }>(
    message = 'Valid from date must be before valid to date',
    path: (keyof T)[] = ['validTo' as keyof T]
  ) => {
    return (data: T) => {
      if (data.validFrom && data.validTo) {
        return data.validFrom < data.validTo;
      }
      return true;
    };
  },

  /**
   * Future date validation refinement
   */
  futureDate: <T extends { validTo?: Date }>(
    message = 'Valid to date must be in the future',
    path: (keyof T)[] = ['validTo' as keyof T]
  ) => {
    return (data: T) => {
      if (data.validTo) {
        return data.validTo > new Date();
      }
      return true;
    };
  },

  /**
   * Business rule validation for percentage cap discounts
   */
  percentageCapBusinessRule: <T extends { maxDiscountAmount?: number; percentage?: number }>(
    message = 'Maximum discount amount cannot be zero when percentage is greater than zero',
    path: (keyof T)[] = ['maxDiscountAmount' as keyof T]
  ) => {
    return (data: T) => {
      if (data.percentage !== undefined && data.maxDiscountAmount !== undefined) {
        return !(data.maxDiscountAmount === 0 && data.percentage > 0);
      }
      return true;
    };
  }
};

/**
 * Common field schemas with configurable limits
 */
export const CommonFieldSchemas = {
  /**
   * Name field schema
   */
  name: () => {
    const limits = getValidationLimits();
    return z.string()
      .min(1, 'Name is required')
      .max(limits.maxNameLength, `Name cannot exceed ${limits.maxNameLength} characters`)
      .transform(val => val.trim().replace(/\s+/g, ' '));
  },

  /**
   * Description field schema
   */
  description: () => {
    const limits = getValidationLimits();
    return z.string()
      .max(limits.maxDescriptionLength, `Description cannot exceed ${limits.maxDescriptionLength} characters`)
      .transform(val => val.trim().replace(/\s+/g, ' '))
      .optional();
  },

  /**
   * Percentage field schema
   */
  percentage: () => {
    const limits = getValidationLimits();
    return z.number()
      .min(0.01, 'Percentage must be greater than 0')
      .max(limits.maxPercentage, `Percentage cannot exceed ${limits.maxPercentage}`);
  },

  /**
   * Max usage field schema
   */
  maxUsage: () => {
    const limits = getValidationLimits();
    return z.number()
      .int('Maximum usage must be an integer')
      .min(1, 'Maximum usage must be at least 1')
      .max(limits.maxUsageLimit, `Maximum usage cannot exceed ${limits.maxUsageLimit}`)
      .optional();
  },

  /**
   * UUID field schema
   */
  uuid: (fieldName = 'ID') => {
    return z.string().uuid(`Invalid ${fieldName} format`);
  },

  /**
   * Date field schema
   */
  date: () => {
    return z.coerce.date();
  },

  /**
   * Positive number field schema
   */
  positiveNumber: (fieldName = 'Value') => {
    return z.number().min(0, `${fieldName} cannot be negative`);
  },

  /**
   * Positive integer field schema
   */
  positiveInteger: (fieldName = 'Value') => {
    return z.number()
      .int(`${fieldName} must be an integer`)
      .positive(`${fieldName} must be a positive integer`);
  },

  /**
   * Search term field schema
   */
  searchTerm: () => {
    const limits = getValidationLimits();
    return z.string()
      .min(1, 'Search term must be at least 1 character')
      .max(limits.maxSearchTermLength, `Search term cannot exceed ${limits.maxSearchTermLength} characters`)
      .optional();
  }
};

/**
 * Common validation patterns
 */
export const ValidationPatterns = {
  /**
   * Apply date range validation to a schema
   */
  withDateRangeValidation: <T extends z.ZodTypeAny>(schema: T) => {
    return schema.refine(
      ValidationRefinements.dateRange(),
      {
        message: 'Valid from date must be before valid to date',
        path: ['validTo']
      }
    ).refine(
      ValidationRefinements.futureDate(),
      {
        message: 'Valid to date must be in the future',
        path: ['validTo']
      }
    );
  },

  /**
   * Apply percentage cap business rule validation
   */
  withPercentageCapValidation: <T extends z.ZodTypeAny>(schema: T) => {
    return schema.refine(
      ValidationRefinements.percentageCapBusinessRule(),
      {
        message: 'Maximum discount amount cannot be zero when percentage is greater than zero',
        path: ['maxDiscountAmount']
      }
    );
  },

  /**
   * Apply pagination validation
   */
  withPaginationValidation: <T extends z.ZodTypeAny>(schema: T) => {
    const limits = getValidationLimits();
    return schema.extend({
      page: z.number()
        .int('Page must be an integer')
        .min(1, 'Page must be at least 1')
        .default(1),
      limit: z.number()
        .int('Limit must be an integer')
        .min(1, 'Limit must be at least 1')
        .max(limits.maxDiscountsPerList, `Limit cannot exceed ${limits.maxDiscountsPerList}`)
        .default(20)
    });
  }
};

/**
 * Error message standardization
 */
export const ErrorMessages = {
  required: (field: string) => `${field} is required`,
  invalid: (field: string) => `Invalid ${field} format`,
  tooLong: (field: string, max: number) => `${field} cannot exceed ${max} characters`,
  tooShort: (field: string, min: number) => `${field} must be at least ${min} characters`,
  outOfRange: (field: string, min: number, max: number) => `${field} must be between ${min} and ${max}`,
  positive: (field: string) => `${field} must be positive`,
  nonNegative: (field: string) => `${field} cannot be negative`,
  integer: (field: string) => `${field} must be an integer`,
  future: (field: string) => `${field} must be in the future`,
  dateRange: () => 'Valid from date must be before valid to date',
  businessRule: (rule: string) => `Business rule violation: ${rule}`
};

/**
 * Validation result helpers
 */
export const ValidationHelpers = {
  /**
   * Extract validation errors in a consistent format
   */
  extractErrors: (error: z.ZodError) => {
    return error.errors.map(err => ({
      field: err.path.join('.'),
      message: err.message,
      code: err.code
    }));
  },

  /**
   * Format validation errors for user display
   */
  formatErrors: (error: z.ZodError) => {
    return ValidationHelpers.extractErrors(error)
      .map(err => `${err.field}: ${err.message}`)
      .join(', ');
  },

  /**
   * Check if validation error is for a specific field
   */
  hasFieldError: (error: z.ZodError, fieldPath: string) => {
    return error.errors.some(err => err.path.join('.') === fieldPath);
  },

  /**
   * Get error message for a specific field
   */
  getFieldError: (error: z.ZodError, fieldPath: string) => {
    const fieldError = error.errors.find(err => err.path.join('.') === fieldPath);
    return fieldError?.message;
  },

  /**
   * Safe parse with detailed error information
   */
  safeParseWithDetails: <T>(schema: z.ZodSchema<T>, data: unknown) => {
    const result = schema.safeParse(data);
    if (!result.success) {
      return {
        success: false,
        errors: ValidationHelpers.extractErrors(result.error),
        formattedError: ValidationHelpers.formatErrors(result.error)
      };
    }
    return {
      success: true,
      data: result.data
    };
  }
};

/**
 * Common validation functions
 */
export const CommonValidations = {
  /**
   * Validate cart items array
   */
  validateCartItems: (cartItems: unknown[]) => {
    const limits = getValidationLimits();
    const errors: string[] = [];

    if (!Array.isArray(cartItems)) {
      errors.push('Cart items must be an array');
      return errors;
    }

    if (cartItems.length === 0) {
      errors.push('Cart must contain at least one item');
    }

    if (cartItems.length > limits.maxCartItems) {
      errors.push(`Cart cannot contain more than ${limits.maxCartItems} items`);
    }

    return errors;
  },

  /**
   * Validate discount data for business rules
   */
  validateDiscountBusinessRules: (discount: any) => {
    const errors: string[] = [];

    // Type-specific validations
    if (discount.type === 'PERCENTAGE_CAP') {
      if (discount.percentage && discount.maxDiscountAmount === 0) {
        errors.push(ErrorMessages.businessRule('Maximum discount amount cannot be zero when percentage is set'));
      }
      
      if (discount.minCartValue < 0) {
        errors.push(ErrorMessages.nonNegative('Minimum cart value'));
      }
    }

    // Date validations
    if (discount.validFrom && discount.validTo) {
      if (discount.validFrom >= discount.validTo) {
        errors.push(ErrorMessages.dateRange());
      }
    }

    // Usage limit validations
    if (discount.maxUsage && discount.maxUsage <= 0) {
      errors.push(ErrorMessages.positive('Maximum usage'));
    }

    return errors;
  },

  /**
   * Validate ID format
   */
  validateId: (id: string, fieldName = 'ID') => {
    const errors: string[] = [];
    
    if (!id) {
      errors.push(ErrorMessages.required(fieldName));
      return errors;
    }

    // UUID format validation
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(id)) {
      errors.push(ErrorMessages.invalid(fieldName));
    }

    return errors;
  }
};

/**
 * Export all validation utilities
 */
export {
  ValidationRefinements,
  CommonFieldSchemas,
  ValidationPatterns,
  ErrorMessages,
  ValidationHelpers,
  CommonValidations
};