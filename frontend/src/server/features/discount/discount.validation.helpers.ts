/**
 * Discount validation helper functions
 * 
 * These functions provide runtime validation for discount data integrity
 * and business rule enforcement.
 */

import type { 
  PercentageCapDiscount, 
  CartItemWithDetails,
  DiscountCreateData 
} from './discount.types';

export class DiscountValidationHelpers {
  /**
   * Validate percentage cap discount business rules
   */
  static validatePercentageCapDiscount(discount: PercentageCapDiscount): string[] {
    const errors: string[] = [];

    // Percentage validation
    if (discount.percentage <= 0 || discount.percentage > 100) {
      errors.push('Percentage must be between 0.01 and 100');
    }

    // Max discount amount validation
    if (discount.maxDiscountAmount < 0) {
      errors.push('Maximum discount amount cannot be negative');
    }

    // Min cart value validation
    if (discount.minCartValue < 0) {
      errors.push('Minimum cart value cannot be negative');
    }

    // Business logic validation
    if (discount.maxDiscountAmount === 0 && discount.percentage > 0) {
      errors.push('Maximum discount amount cannot be zero when percentage is greater than zero');
    }

    return errors;
  }

  /**
   * Validate discount date range
   */
  static validateDateRange(validFrom: Date, validTo: Date): string[] {
    const errors: string[] = [];

    if (validFrom >= validTo) {
      errors.push('Valid from date must be before valid to date');
    }

    // Don't allow discounts that are already expired when created
    const now = new Date();
    if (validTo <= now) {
      errors.push('Valid to date must be in the future');
    }

    return errors;
  }

  /**
   * Validate cart items for discount calculation
   */
  static validateCartItems(cartItems: CartItemWithDetails[]): string[] {
    const errors: string[] = [];

    if (!cartItems || cartItems.length === 0) {
      errors.push('Cart must contain at least one item');
    }

    cartItems.forEach((item, index) => {
      if (!Number.isInteger(item.skuId) || item.skuId <= 0) {
        errors.push(`Cart item ${index + 1}: SKU ID must be a positive integer`);
      }

      if (item.variantSkuId !== undefined && (!Number.isInteger(item.variantSkuId) || item.variantSkuId <= 0)) {
        errors.push(`Cart item ${index + 1}: Variant SKU ID must be a positive integer`);
      }

      if (!Number.isInteger(item.quantity) || item.quantity < 1) {
        errors.push(`Cart item ${index + 1}: Quantity must be at least 1`);
      }

      if (typeof item.pricePerUnit !== 'number' || item.pricePerUnit < 0) {
        errors.push(`Cart item ${index + 1}: Price per unit cannot be negative`);
      }

      if (typeof item.mrpPerUnit !== 'number' || item.mrpPerUnit < 0) {
        errors.push(`Cart item ${index + 1}: MRP per unit cannot be negative`);
      }
    });

    return errors;
  }

  /**
   * Validate discount creation data
   */
  static validateDiscountCreateData(data: DiscountCreateData): string[] {
    const errors: string[] = [];

    // Name validation
    if (!data.name || data.name.trim().length === 0) {
      errors.push('Discount name is required');
    } else if (data.name.length > 100) {
      errors.push('Discount name cannot exceed 100 characters');
    }

    // Description validation
    if (data.description && data.description.length > 500) {
      errors.push('Discount description cannot exceed 500 characters');
    }

    // Date range validation
    errors.push(...this.validateDateRange(data.validFrom, data.validTo));

    // Max usage validation
    if (data.maxUsage !== undefined && (!Number.isInteger(data.maxUsage) || data.maxUsage < 1)) {
      errors.push('Maximum usage must be a positive integer');
    }

    // Type-specific validation
    if (data.type === 'PERCENTAGE_CAP') {
      const percentageDiscount = data as PercentageCapDiscount;
      errors.push(...this.validatePercentageCapDiscount(percentageDiscount));
    }

    return errors;
  }

  /**
   * Sanitize discount name (remove extra spaces, trim)
   */
  static sanitizeDiscountName(name: string): string {
    return name.trim().replace(/\s+/g, ' ');
  }

  /**
   * Sanitize discount description
   */
  static sanitizeDiscountDescription(description?: string): string | undefined {
    if (!description) return undefined;
    return description.trim().replace(/\s+/g, ' ');
  }
}