/**
 * Unit tests for Discount Engine
 * 
 * Tests cover the discount engine interface, calculator registration system,
 * discount calculation logic, validation, and error handling.
 */

import {
  DefaultDiscountEngine,
  DefaultDiscountEngineFactory,
  getDefaultDiscountEngine,
  resetDefaultDiscountEngine,
  createDiscountEngineFactory
} from '../discount.engine';
import type {
  DiscountEngine,
  DiscountCalculator,
  DiscountEngineConfig,
  DiscountEngineFactory
} from '../discount.engine.interface';
import {
  CalculatorRegistrationError,
  DiscountEngineError,
  DiscountValidationError,
  DiscountEngineUtils
} from '../discount.engine.interface';
import type {
  Discount,
  CartItemWithDetails,
  DiscountType,
  DiscountCalculationResult
} from '../discount.types';

// Mock logger to avoid console output during tests
jest.mock('@/lib/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn()
  }
}));

// Test data fixtures
const createMockCartItems = (overrides: Partial<CartItemWithDetails>[] = []): CartItemWithDetails[] => {
  const defaults: CartItemWithDetails[] = [
    {
      skuId: 1,
      quantity: 2,
      pricePerUnit: 100,
      mrpPerUnit: 120,
      name: 'Test Product 1'
    },
    {
      skuId: 2,
      quantity: 1,
      pricePerUnit: 200,
      mrpPerUnit: 250,
      name: 'Test Product 2'
    }
  ];

  return overrides.length > 0 
    ? overrides.map((override, index) => ({ ...defaults[index] || defaults[0], ...override }))
    : defaults;
};

const createMockDiscount = (overrides: Partial<Discount> = {}): Discount => ({
  id: 'test-discount-123',
  name: 'Test Discount',
  description: 'Test discount description',
  type: 'PERCENTAGE_CAP' as DiscountType,
  isActive: true,
  validFrom: new Date('2024-01-01'),
  validTo: new Date('2024-12-31'),
  createdAt: new Date('2024-01-01T10:00:00Z'),
  updatedAt: new Date('2024-01-01T10:00:00Z'),
  usageCount: 0,
  percentage: 10,
  maxDiscountAmount: 50,
  minCartValue: 300,
  maxUsage: 1000,
  ...overrides
});

// Mock calculator for testing
class MockDiscountCalculator implements DiscountCalculator {
  readonly type: DiscountType = 'PERCENTAGE_CAP';

  canApply(discount: Discount, cartItems: CartItemWithDetails[]): boolean {
    const cartTotal = DiscountEngineUtils.calculateCartTotal(cartItems);
    return cartTotal >= (discount as any).minCartValue;
  }

  calculate(discount: Discount, cartItems: CartItemWithDetails[]): number {
    const cartTotal = DiscountEngineUtils.calculateCartTotal(cartItems);
    const percentage = (discount as any).percentage / 100;
    const discountAmount = cartTotal * percentage;
    const maxDiscount = (discount as any).maxDiscountAmount;
    
    return Math.min(discountAmount, maxDiscount);
  }

  validate(discount: Discount): string[] {
    const errors: string[] = [];
    const percentageDiscount = discount as any;

    if (!percentageDiscount.percentage || percentageDiscount.percentage <= 0) {
      errors.push('Percentage must be greater than 0');
    }

    if (percentageDiscount.percentage > 100) {
      errors.push('Percentage cannot exceed 100');
    }

    if (!percentageDiscount.maxDiscountAmount || percentageDiscount.maxDiscountAmount <= 0) {
      errors.push('Maximum discount amount must be greater than 0');
    }

    if (!percentageDiscount.minCartValue || percentageDiscount.minCartValue < 0) {
      errors.push('Minimum cart value cannot be negative');
    }

    return errors;
  }
}

// Faulty calculator for error testing
class FaultyCalculator implements DiscountCalculator {
  readonly type: DiscountType = 'PERCENTAGE_CAP';

  canApply(): boolean {
    throw new Error('Faulty canApply implementation');
  }

  calculate(): number {
    throw new Error('Faulty calculate implementation');
  }

  validate(): string[] {
    throw new Error('Faulty validate implementation');
  }
}

describe('DiscountEngine', () => {
  let engine: DiscountEngine;
  let mockCalculator: MockDiscountCalculator;

  beforeEach(() => {
    engine = new DefaultDiscountEngine();
    mockCalculator = new MockDiscountCalculator();
  });

  afterEach(() => {
    resetDefaultDiscountEngine();
  });

  describe('Calculator Registration', () => {
    test('should register a calculator successfully', () => {
      expect(() => engine.registerCalculator(mockCalculator)).not.toThrow();
      
      const registeredCalculator = engine.getCalculator('PERCENTAGE_CAP');
      expect(registeredCalculator).toBe(mockCalculator);
    });

    test('should throw error when registering duplicate calculator', () => {
      engine.registerCalculator(mockCalculator);
      
      const duplicateCalculator = new MockDiscountCalculator();
      expect(() => engine.registerCalculator(duplicateCalculator))
        .toThrow(CalculatorRegistrationError);
    });

    test('should validate calculator interface during registration', () => {
      const invalidCalculator = {
        type: 'PERCENTAGE_CAP' as DiscountType,
        // Missing required methods
      } as DiscountCalculator;

      expect(() => engine.registerCalculator(invalidCalculator))
        .toThrow(CalculatorRegistrationError);
    });

    test('should unregister calculator successfully', () => {
      engine.registerCalculator(mockCalculator);
      
      const unregistered = engine.unregisterCalculator('PERCENTAGE_CAP');
      expect(unregistered).toBe(true);
      
      const calculator = engine.getCalculator('PERCENTAGE_CAP');
      expect(calculator).toBeUndefined();
    });

    test('should return false when unregistering non-existent calculator', () => {
      const unregistered = engine.unregisterCalculator('PERCENTAGE_CAP');
      expect(unregistered).toBe(false);
    });

    test('should return all registered types', () => {
      engine.registerCalculator(mockCalculator);
      
      const types = engine.getRegisteredTypes();
      expect(types).toEqual(['PERCENTAGE_CAP']);
    });
  });

  describe('Discount Calculation', () => {
    beforeEach(() => {
      engine.registerCalculator(mockCalculator);
    });

    test('should calculate discounts successfully', async () => {
      const cartItems = createMockCartItems(); // Total: 400
      const discount = createMockDiscount({
        percentage: 10,
        maxDiscountAmount: 50,
        minCartValue: 300
      });

      const result = await engine.calculateDiscounts(cartItems, [discount]);

      expect(result).toEqual({
        totalDiscount: 40, // 10% of 400, under the cap of 50
        appliedDiscounts: [{
          discountId: discount.id,
          discountName: discount.name,
          discountAmount: 40,
          discountType: discount.type
        }],
        originalTotal: 400,
        finalTotal: 360,
        savings: 40
      });
    });

    test('should apply discount cap correctly', async () => {
      const cartItems = createMockCartItems([
        { pricePerUnit: 500, quantity: 2 } // Total: 1000
      ]);
      const discount = createMockDiscount({
        percentage: 10,
        maxDiscountAmount: 50, // Cap at 50
        minCartValue: 300
      });

      const result = await engine.calculateDiscounts(cartItems, [discount]);

      expect(result.totalDiscount).toBe(50); // Capped at 50, not 100
      expect(result.finalTotal).toBe(950);
    });

    test('should not apply discount when cart is below minimum value', async () => {
      const cartItems = createMockCartItems([
        { pricePerUnit: 50, quantity: 2 } // Total: 100
      ]);
      const discount = createMockDiscount({
        percentage: 10,
        maxDiscountAmount: 50,
        minCartValue: 300 // Cart total is below this
      });

      const result = await engine.calculateDiscounts(cartItems, [discount]);

      expect(result.totalDiscount).toBe(0);
      expect(result.appliedDiscounts).toHaveLength(0);
      expect(result.finalTotal).toBe(100);
    });

    test('should handle multiple discounts in non-stacking mode', async () => {
      const cartItems = createMockCartItems(); // Total: 400
      const discount1 = createMockDiscount({
        id: 'discount-1',
        name: 'Discount 1',
        percentage: 10,
        maxDiscountAmount: 30,
        minCartValue: 300
      });
      const discount2 = createMockDiscount({
        id: 'discount-2',
        name: 'Discount 2',
        percentage: 5,
        maxDiscountAmount: 50,
        minCartValue: 300
      });

      const result = await engine.calculateDiscounts(cartItems, [discount1, discount2]);

      // Should apply the better discount (discount1: 30 vs discount2: 20)
      expect(result.totalDiscount).toBe(30);
      expect(result.appliedDiscounts).toHaveLength(1);
      expect(result.appliedDiscounts[0].discountId).toBe('discount-1');
    });

    test('should handle multiple discounts in stacking mode', async () => {
      engine.updateConfig({ allowStacking: true });
      
      const cartItems = createMockCartItems(); // Total: 400
      const discount1 = createMockDiscount({
        id: 'discount-1',
        name: 'Discount 1',
        percentage: 10,
        maxDiscountAmount: 30,
        minCartValue: 300
      });
      const discount2 = createMockDiscount({
        id: 'discount-2',
        name: 'Discount 2',
        percentage: 5,
        maxDiscountAmount: 50,
        minCartValue: 300
      });

      const result = await engine.calculateDiscounts(cartItems, [discount1, discount2]);

      // Should apply both discounts
      expect(result.totalDiscount).toBe(50); // 30 + 20
      expect(result.appliedDiscounts).toHaveLength(2);
    });

    test('should ensure final total is never negative', async () => {
      const cartItems = createMockCartItems([
        { pricePerUnit: 10, quantity: 1 } // Total: 10
      ]);
      const discount = createMockDiscount({
        percentage: 100,
        maxDiscountAmount: 50, // More than cart total
        minCartValue: 5
      });

      const result = await engine.calculateDiscounts(cartItems, [discount]);

      expect(result.finalTotal).toBe(0); // Should not be negative
    });

    test('should validate cart items before calculation', async () => {
      const invalidCartItems = [
        { skuId: 0, quantity: 1, pricePerUnit: 100, mrpPerUnit: 120 } // Invalid SKU ID
      ] as CartItemWithDetails[];

      await expect(engine.calculateDiscounts(invalidCartItems, []))
        .rejects.toThrow(DiscountEngineError);
    });

    test('should handle empty cart', async () => {
      await expect(engine.calculateDiscounts([], []))
        .rejects.toThrow(DiscountEngineError);
    });

    test('should handle calculator errors gracefully', async () => {
      // Create a fresh engine to avoid registration conflicts
      const testEngine = new DefaultDiscountEngine();
      const faultyCalculator = new FaultyCalculator();
      testEngine.registerCalculator(faultyCalculator);

      const cartItems = createMockCartItems();
      const discount = createMockDiscount();

      const result = await testEngine.calculateDiscounts(cartItems, [discount]);

      // Should return no discounts when calculator fails
      expect(result.totalDiscount).toBe(0);
      expect(result.appliedDiscounts).toHaveLength(0);
    });
  });

  describe('Discount Validation', () => {
    beforeEach(() => {
      engine.registerCalculator(mockCalculator);
    });

    test('should validate discount successfully', () => {
      const validDiscount = createMockDiscount({
        percentage: 10,
        maxDiscountAmount: 50,
        minCartValue: 300
      });

      const result = engine.validateDiscount(validDiscount);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    test('should return errors for invalid discount', () => {
      const invalidDiscount = createMockDiscount({
        percentage: 0, // Invalid
        maxDiscountAmount: -10, // Invalid
        minCartValue: -5 // Invalid
      });

      const result = engine.validateDiscount(invalidDiscount);

      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    test('should handle missing calculator during validation', () => {
      const discount = createMockDiscount({ type: 'UNKNOWN_TYPE' as DiscountType });

      const result = engine.validateDiscount(discount);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('No calculator registered for discount type: UNKNOWN_TYPE');
    });

    test('should validate discount combinations', () => {
      const discount1 = createMockDiscount({ id: 'discount-1' });
      const discount2 = createMockDiscount({ id: 'discount-2' });

      const result = engine.validateDiscountCombination([discount1, discount2]);

      // Should fail in non-stacking mode
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Discount stacking is not allowed');
    });

    test('should validate discount combinations in stacking mode', () => {
      engine.updateConfig({ allowStacking: true });

      const discount1 = createMockDiscount({ id: 'discount-1' });
      const discount2 = createMockDiscount({ id: 'discount-2' });

      const result = engine.validateDiscountCombination([discount1, discount2]);

      expect(result.isValid).toBe(true);
    });

    test('should enforce maximum discounts per cart', () => {
      engine.updateConfig({ maxDiscountsPerCart: 2 });

      const discounts = Array.from({ length: 3 }, (_, i) => 
        createMockDiscount({ id: `discount-${i}` })
      );

      const result = engine.validateDiscountCombination(discounts);

      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('Too many discounts'))).toBe(true);
    });
  });

  describe('Configuration Management', () => {
    test('should return current configuration', () => {
      const config = engine.getConfig();

      expect(config).toEqual({
        maxDiscountsPerCart: 10,
        allowStacking: false,
        calculationTimeoutMs: 5000,
        enableDebugLogging: false
      });
    });

    test('should update configuration', () => {
      engine.updateConfig({
        allowStacking: true,
        maxDiscountsPerCart: 5
      });

      const config = engine.getConfig();

      expect(config.allowStacking).toBe(true);
      expect(config.maxDiscountsPerCart).toBe(5);
      expect(config.calculationTimeoutMs).toBe(5000); // Should preserve existing values
    });
  });

  describe('Engine Statistics', () => {
    beforeEach(() => {
      engine.registerCalculator(mockCalculator);
    });

    test('should track calculation statistics', async () => {
      const cartItems = createMockCartItems();
      const discount = createMockDiscount();

      await engine.calculateDiscounts(cartItems, [discount]);
      await engine.calculateDiscounts(cartItems, [discount]);

      const stats = engine.getEngineStats();

      expect(stats.registeredCalculators).toBe(1);
      expect(stats.totalCalculations).toBe(2);
      expect(stats.averageCalculationTimeMs).toBeGreaterThanOrEqual(0);
      expect(stats.lastCalculationAt).toBeInstanceOf(Date);
    });

    test('should track errors in statistics', async () => {
      // Create a fresh engine to avoid registration conflicts
      const testEngine = new DefaultDiscountEngine();
      const faultyCalculator = new FaultyCalculator();
      testEngine.registerCalculator(faultyCalculator);

      const cartItems = createMockCartItems();
      const discount = createMockDiscount();

      await testEngine.calculateDiscounts(cartItems, [discount]);

      const stats = testEngine.getEngineStats();

      expect(stats.errors.total).toBeGreaterThan(0);
      expect(stats.errors.recent.length).toBeGreaterThan(0);
    });
  });

  describe('Utility Functions', () => {
    describe('DiscountEngineUtils', () => {
      test('should calculate cart total correctly', () => {
        const cartItems = createMockCartItems();
        const total = DiscountEngineUtils.calculateCartTotal(cartItems);

        expect(total).toBe(400); // (100 * 2) + (200 * 1)
      });

      test('should validate cart items', () => {
        const validItems = createMockCartItems();
        const errors = DiscountEngineUtils.validateCartItems(validItems);

        expect(errors).toHaveLength(0);
      });

      test('should detect invalid cart items', () => {
        const invalidItems = [
          { skuId: 0, quantity: 1, pricePerUnit: 100, mrpPerUnit: 120 }, // Invalid SKU
          { skuId: 1, quantity: 0, pricePerUnit: 100, mrpPerUnit: 120 }, // Invalid quantity
          { skuId: 2, quantity: 1, pricePerUnit: -10, mrpPerUnit: 120 } // Invalid price
        ] as CartItemWithDetails[];

        const errors = DiscountEngineUtils.validateCartItems(invalidItems);

        expect(errors.length).toBeGreaterThan(0);
      });

      test('should sort discounts by priority', () => {
        const discount1 = createMockDiscount({ 
          id: 'discount-1', 
          createdAt: new Date('2024-01-01') 
        });
        const discount2 = createMockDiscount({ 
          id: 'discount-2', 
          createdAt: new Date('2024-01-02') 
        });

        const sorted = DiscountEngineUtils.sortDiscountsByPriority([discount1, discount2]);

        expect(sorted[0].id).toBe('discount-2'); // Newer first
        expect(sorted[1].id).toBe('discount-1');
      });

      test('should check discount combination compatibility', () => {
        const discount1 = createMockDiscount({ type: 'PERCENTAGE_CAP' });
        const discount2 = createMockDiscount({ type: 'PERCENTAGE_CAP' });

        const canCombine = DiscountEngineUtils.canCombineDiscounts(discount1, discount2);

        expect(canCombine).toBe(false); // Same type cannot be combined
      });
    });
  });

  describe('Factory and Singleton', () => {
    test('should create engine through factory', () => {
      const factory = new DefaultDiscountEngineFactory();
      const engine = factory.createEngine();

      expect(engine).toBeInstanceOf(DefaultDiscountEngine);
    });

    test('should create engine with defaults through factory', () => {
      const factory = new DefaultDiscountEngineFactory();
      const engine = factory.createEngineWithDefaults();

      expect(engine).toBeInstanceOf(DefaultDiscountEngine);
    });

    test('should return singleton instance', () => {
      const engine1 = getDefaultDiscountEngine();
      const engine2 = getDefaultDiscountEngine();

      expect(engine1).toBe(engine2);
    });

    test('should reset singleton instance', () => {
      const engine1 = getDefaultDiscountEngine();
      resetDefaultDiscountEngine();
      const engine2 = getDefaultDiscountEngine();

      expect(engine1).not.toBe(engine2);
    });

    test('should create factory instance', () => {
      const factory = createDiscountEngineFactory();

      expect(factory).toBeInstanceOf(DefaultDiscountEngineFactory);
    });
  });

  describe('Error Handling', () => {
    test('should handle calculator registration errors', () => {
      engine.registerCalculator(mockCalculator);

      expect(() => engine.registerCalculator(mockCalculator))
        .toThrow(CalculatorRegistrationError);
    });

    test('should handle validation errors gracefully', () => {
      const faultyCalculator = new FaultyCalculator();
      engine.registerCalculator(faultyCalculator);

      const discount = createMockDiscount();
      const result = engine.validateDiscount(discount);

      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    test('should record and limit error history', async () => {
      const faultyCalculator = new FaultyCalculator();
      engine.registerCalculator(faultyCalculator);

      const cartItems = createMockCartItems();
      const discount = createMockDiscount();

      // Generate multiple errors
      for (let i = 0; i < 5; i++) {
        await engine.calculateDiscounts(cartItems, [discount]);
      }

      const stats = engine.getEngineStats();
      expect(stats.errors.total).toBe(5);
      expect(stats.errors.recent.length).toBe(5);
    });
  });

  describe('Debug Logging', () => {
    test('should enable debug logging', async () => {
      const { logger } = require('@/lib/logger');
      const loggerSpy = jest.spyOn(logger, 'debug').mockImplementation();
      
      engine.updateConfig({ enableDebugLogging: true });
      engine.registerCalculator(mockCalculator);

      const cartItems = createMockCartItems();
      const discount = createMockDiscount();

      await engine.calculateDiscounts(cartItems, [discount]);

      expect(loggerSpy).toHaveBeenCalled();
      
      loggerSpy.mockRestore();
    });
  });
});